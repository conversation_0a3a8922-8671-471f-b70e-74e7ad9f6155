---
description:
globs: components/*/demo/**
alwaysApply: false
---

# Demo 规范

- demo 代码尽可能简洁
- 避免冗余代码，方便用户复制到项目直接使用
- 每个 demo 聚焦展示一个功能点
- 提供中英文两个版本的说明
- demo 文件命名：
  - 英文 demo: index.en-US.md
  - 中文 demo: index.zh-CN.md
- 确保 demo 在各种尺寸下都能正常展示
- 对于复杂交互提供必要的操作说明

## 文件组织

- 每个组件演示包含 `.md`（说明文档）和 `.ts`（实际代码）两部分
- 位置：hooks 目录下的 `src` 子目录，如 `packages/hooks/src/useHover`
- 文件名应简洁地描述示例内容

## MD 文档规范

- 必须包含 `## zh-CN` 和 `## en-US` 两种语言说明
- 内容简洁明了，突出组件特性和用法
- 避免冗长段落，必要时使用列表或粗体
- 标注注意事项和实验性功能

## 代码质量

- 实用且专注于单一功能
- 关键处添加简洁注释
- 使用有意义的数据和变量
- 优先使用 ahooks 内置 hook 或者公共方法，减少外部依赖

## 质量要求

- 确保代码运行正常，无控制台错误
- 适配常见浏览器
- 避免过时 API，及时更新到新推荐用法

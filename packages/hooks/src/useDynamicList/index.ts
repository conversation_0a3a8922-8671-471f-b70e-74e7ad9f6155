import { useCallback, useRef, useState } from 'react';
import isDev from '../utils/isDev';

const useDynamicList = <T>(initialList: T[] = []) => {
  const counterRef = useRef(-1);
  const keyList = useRef<number[]>([]);
  const keyToIndexMap = useRef<Map<number, number>>(new Map());

  const updateKeyToIndexMap = useCallback(() => {
    keyToIndexMap.current.clear();
    keyList.current.forEach((key, index) => {
      keyToIndexMap.current.set(key, index);
    });
  }, []);

  const setKey = useCallback((index: number) => {
    counterRef.current += 1;
    keyList.current.splice(index, 0, counterRef.current);
    updateKeyToIndexMap();
  }, [updateKeyToIndexMap]);

  const [list, setList] = useState(() => {
    for (let index = 0; index < initialList.length; index++) {
      counterRef.current += 1;
      keyList.current.push(counterRef.current);
    }
    updateKeyToIndexMap();
    return initialList;
  });

  const resetList = useCallback((newList: T[]) => {
    keyList.current = [];
    counterRef.current = -1;

    for (let index = 0; index < newList.length; index++) {
      counterRef.current += 1;
      keyList.current.push(counterRef.current);
    }
    updateKeyToIndexMap();

    setList(newList);
  }, [updateKeyToIndexMap]);

  const insert = useCallback((index: number, item: T) => {
    setList((l) => {
      const temp = [...l];
      temp.splice(index, 0, item);
      counterRef.current += 1;
      keyList.current.splice(index, 0, counterRef.current);
      updateKeyToIndexMap();
      return temp;
    });
  }, [updateKeyToIndexMap]);

  const getKey = useCallback((index: number) => keyList.current[index], []);

  const getIndex = useCallback((key: number) => {
    return keyToIndexMap.current.get(key) ?? -1;
  }, []);

  const merge = useCallback((index: number, items: T[]) => {
    setList((l) => {
      const temp = [...l];
      const newKeys: number[] = [];

      for (let i = 0; i < items.length; i++) {
        counterRef.current += 1;
        newKeys.push(counterRef.current);
      }

      keyList.current.splice(index, 0, ...newKeys);
      temp.splice(index, 0, ...items);
      updateKeyToIndexMap();
      return temp;
    });
  }, [updateKeyToIndexMap]);

  const replace = useCallback((index: number, item: T) => {
    setList((l) => {
      const temp = [...l];
      temp[index] = item;
      return temp;
    });
  }, []);

  const remove = useCallback((index: number) => {
    setList((l) => {
      const temp = [...l];
      temp.splice(index, 1);
      keyList.current.splice(index, 1);
      updateKeyToIndexMap();
      return temp;
    });
  }, [updateKeyToIndexMap]);

  const batchRemove = useCallback((indexes: number[]) => {
    if (!Array.isArray(indexes)) {
      if (isDev) {
        console.error(
          `\`indexes\` parameter of \`batchRemove\` function expected to be an array, but got "${typeof indexes}".`,
        );
      }
      return;
    }
    if (!indexes.length) {
      return;
    }

    const indexSet = new Set(indexes);

    setList((prevList) => {
      const newKeyList: number[] = [];
      const newList: T[] = [];

      for (let i = 0; i < prevList.length; i++) {
        if (!indexSet.has(i)) {
          newList.push(prevList[i]);
          newKeyList.push(keyList.current[i]);
        }
      }

      keyList.current = newKeyList;
      updateKeyToIndexMap();
      return newList;
    });
  }, [updateKeyToIndexMap]);

  const move = useCallback((oldIndex: number, newIndex: number) => {
    if (oldIndex === newIndex) {
      return;
    }
    setList((l) => {
      const newList = [...l];
      const newKeyList = [...keyList.current];

      const [movedItem] = newList.splice(oldIndex, 1);
      const [movedKey] = newKeyList.splice(oldIndex, 1);

      newList.splice(newIndex, 0, movedItem);
      newKeyList.splice(newIndex, 0, movedKey);

      keyList.current = newKeyList;
      updateKeyToIndexMap();
      return newList;
    });
  }, [updateKeyToIndexMap]);

  const push = useCallback((item: T) => {
    setList((l) => {
      counterRef.current += 1;
      keyList.current.push(counterRef.current);
      updateKeyToIndexMap();
      return [...l, item];
    });
  }, [updateKeyToIndexMap]);

  const pop = useCallback(() => {
    setList((l) => {
      if (l.length === 0) return l;
      keyList.current.pop();
      updateKeyToIndexMap();
      return l.slice(0, -1);
    });
  }, [updateKeyToIndexMap]);

  const unshift = useCallback((item: T) => {
    setList((l) => {
      counterRef.current += 1;
      keyList.current.unshift(counterRef.current);
      updateKeyToIndexMap();
      return [item, ...l];
    });
  }, [updateKeyToIndexMap]);

  const shift = useCallback(() => {
    setList((l) => {
      if (l.length === 0) return l;
      keyList.current.shift();
      updateKeyToIndexMap();
      return l.slice(1);
    });
  }, [updateKeyToIndexMap]);

  const sortList = useCallback((result: T[]) => {
    const itemsWithKeys = result.map((item, index) => ({
      key: index,
      item,
      originalIndex: getIndex(index)
    }));

    return itemsWithKeys
      .filter((item) => item.originalIndex !== -1 && item.item != null)
      .sort((a, b) => a.originalIndex - b.originalIndex)
      .map((item) => item.item);
  }, [getIndex]);

  return {
    list,
    insert,
    merge,
    replace,
    remove,
    batchRemove,
    getKey,
    getIndex,
    move,
    push,
    pop,
    unshift,
    shift,
    sortList,
    resetList,
  };
};

export default useDynamicList;

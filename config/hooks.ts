export const menus = [
  {
    title: 'useRequest',
    children: [
      'useRequest/doc/index',
      'useRequest/doc/basic',
      'useRequest/doc/loadingDelay',
      'useRequest/doc/polling',
      'useRequest/doc/ready',
      'useRequest/doc/refreshDeps',
      'useRequest/doc/refreshOnWindowFocus',
      'useRequest/doc/debounce',
      'useRequest/doc/throttle',
      'useRequest/doc/cache',
      'useRequest/doc/retry',
    ],
  },
  {
    title: 'Scene',
    children: [
      'useAntdTable',
      'useFusionTable',
      'useInfiniteScroll',
      'usePagination',
      'useDynamicList',
      'useVirtualList',
      'useHistoryTravel',
      'useNetwork',
      'useSelections',
      'useCountDown',
      'useCounter',
      'useTextSelection',
      'useWebSocket',
      'useTheme',
    ],
  },
  {
    title: 'LifeCycle',
    children: ['useMount', 'useUnmount', 'useUnmountedRef'],
  },
  {
    title: 'State',
    children: [
      'useSetState',
      'useBoolean',
      'useToggle',
      'use-url-state',
      'useCookieState',
      'useLocalStorageState',
      'useSessionStorageState',
      'useDebounce',
      'useThrottle',
      'useMap',
      'useSet',
      'usePrevious',
      'useRafState',
      'useSafeState',
      'useGetState',
      'useResetState',
    ],
  },
  {
    title: 'Effect',
    children: [
      'useUpdateEffect',
      'useUpdateLayoutEffect',
      'useAsyncEffect',
      'useDebounceEffect',
      'useDebounceFn',
      'useThrottleFn',
      'useThrottleEffect',
      'useDeepCompareEffect',
      'useDeepCompareLayoutEffect',
      'useInterval',
      'useRafInterval',
      'useTimeout',
      'useRafTimeout',
      'useLockFn',
      'useUpdate',
    ],
  },
  {
    title: 'Dom',
    children: [
      'useEventListener',
      'useClickAway',
      'useDocumentVisibility',
      'useDrop',
      'useEventTarget',
      'useExternal',
      'useTitle',
      'useFavicon',
      'useFullscreen',
      'useHover',
      'useMutationObserver',
      'useInViewport',
      'useKeyPress',
      'useLongPress',
      'useMouse',
      'useResponsive',
      'useScroll',
      'useSize',
      'useFocusWithin',
    ],
  },
  {
    title: 'Advanced',
    children: [
      'useControllableValue',
      'useCreation',
      'useEventEmitter',
      'useIsomorphicLayoutEffect',
      'useLatest',
      'useMemoizedFn',
      'useReactive',
    ],
  },
  {
    title: 'Dev',
    children: ['useTrackedEffect', 'useWhyDidYouUpdate'],
  },
];

---
description: 规范项目文档和 Changelog
globs: ["**/CHANGELOG*.md", "components/**/index.*.md"]
alwaysApply: false
---

# Changelog Emoji 规范

- 🐞 Bug 修复
- 💄 样式更新或 token 更新
- 🆕 新增特性，新增属性
- 🔥 极其值得关注的新增特性
- 🇺🇸🇨🇳🇬🇧 国际化改动
- 📖 📝 文档或网站改进
- ✅ 新增或更新测试用例
- 🛎 更新警告/提示信息
- ⌨️ ♿ 可访问性增强
- 🗑 废弃或移除
- 🛠 重构或工具链优化
- ⚡️ 性能提升

# 文档规范

- 提供中英文两个版本
- 新属性需声明可用的版本号
- 属性命名符合 API 命名规则
- hook 文档包含：使用场景、基础用法、API 说明
- 文档示例应简洁明了
- 属性的描述应清晰易懂
- 对复杂功能提供详细说明
- 加入 TypeScript 定义
- 提供常见问题解答
- 更新文档时同步更新中英文版本

## 其他要求

- 新增属性时，建议用易于理解的语言描述用户可以感知的变化
- 存在破坏性改动时，尽量给出原始的 PR 链接，社区提交的 PR 改动加上提交者的链接

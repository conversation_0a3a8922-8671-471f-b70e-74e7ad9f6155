import { useEffect, useRef, useState, useCallback } from 'react';
import useLatest from '../useLatest';
import useMemoizedFn from '../useMemoizedFn';
import useUnmount from '../useUnmount';

export enum ReadyState {
  Connecting = 0,
  Open = 1,
  Closing = 2,
  Closed = 3,
}

export interface Options {
  reconnectLimit?: number;
  reconnectInterval?: number;
  manual?: boolean;
  onOpen?: (event: WebSocketEventMap['open'], instance: WebSocket) => void;
  onClose?: (event: WebSocketEventMap['close'], instance: WebSocket) => void;
  onMessage?: (message: WebSocketEventMap['message'], instance: WebSocket) => void;
  onError?: (event: WebSocketEventMap['error'], instance: WebSocket) => void;

  protocols?: string | string[];
}

export interface Result {
  latestMessage?: WebSocketEventMap['message'];
  sendMessage: WebSocket['send'];
  disconnect: () => void;
  connect: () => void;
  readyState: ReadyState;
  webSocketIns?: WebSocket;
}

function useWebSocket(socketUrl: string, options: Options = {}): Result {
  const {
    reconnectLimit = 3,
    reconnectInterval = 3 * 1000,
    manual = false,
    onOpen,
    onClose,
    onMessage,
    onError,
    protocols,
  } = options;

  const onOpenRef = useLatest(onOpen);
  const onCloseRef = useLatest(onClose);
  const onMessageRef = useLatest(onMessage);
  const onErrorRef = useLatest(onError);

  const reconnectTimesRef = useRef(0);
  const reconnectTimerRef = useRef<ReturnType<typeof setTimeout>>();
  const websocketRef = useRef<WebSocket>();
  const unmountedRef = useRef(false);

  const [latestMessage, setLatestMessage] = useState<MessageEvent>();
  const [readyState, setReadyState] = useState<ReadyState>(ReadyState.Closed);

  const clearReconnectTimer = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = undefined;
    }
  }, []);

  const reconnect = useCallback(() => {
    if (unmountedRef.current) {
      return;
    }

    if (
      reconnectTimesRef.current < reconnectLimit &&
      websocketRef.current?.readyState !== ReadyState.Open
    ) {
      clearReconnectTimer();

      reconnectTimerRef.current = setTimeout(() => {
        if (!unmountedRef.current) {
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          connectWs();
          reconnectTimesRef.current++;
        }
      }, reconnectInterval);
    }
  }, [reconnectLimit, reconnectInterval, clearReconnectTimer]);

  const connectWs = useCallback(() => {
    if (unmountedRef.current) {
      return;
    }

    clearReconnectTimer();

    if (websocketRef.current) {
      websocketRef.current.close();
    }

    try {
      const ws = new WebSocket(socketUrl, protocols);
      setReadyState(ReadyState.Connecting);

      ws.onerror = (event) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        onErrorRef.current?.(event, ws);
        setReadyState(ws.readyState || ReadyState.Closed);
        reconnect();
      };

      ws.onopen = (event) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        onOpenRef.current?.(event, ws);
        reconnectTimesRef.current = 0;
        setReadyState(ws.readyState || ReadyState.Open);
      };

      ws.onmessage = (message: WebSocketEventMap['message']) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        onMessageRef.current?.(message, ws);
        setLatestMessage(message);
      };

      ws.onclose = (event) => {
        if (unmountedRef.current) {
          return;
        }
        onCloseRef.current?.(event, ws);

        if (websocketRef.current === ws) {
          setReadyState(ws.readyState || ReadyState.Closed);
          reconnect();
        }
      };

      websocketRef.current = ws;
    } catch (error) {
      setReadyState(ReadyState.Closed);
      onErrorRef.current?.(error as Event, websocketRef.current!);
    }
  }, [socketUrl, protocols, clearReconnectTimer, reconnect]);

  const sendMessage = useCallback((message: string | ArrayBufferLike | Blob | ArrayBufferView) => {
    if (readyState === ReadyState.Open && websocketRef.current) {
      try {
        websocketRef.current.send(message);
      } catch (error) {
        console.error('Failed to send message:', error);
        throw error;
      }
    } else {
      throw new Error(`WebSocket is not connected. Current state: ${ReadyState[readyState]}`);
    }
  }, [readyState]);

  const connect = useCallback(() => {
    reconnectTimesRef.current = 0;
    connectWs();
  }, [connectWs]);

  const disconnect = useCallback(() => {
    clearReconnectTimer();
    reconnectTimesRef.current = reconnectLimit;

    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = undefined;
    }

    setReadyState(ReadyState.Closed);
  }, [clearReconnectTimer, reconnectLimit]);

  useEffect(() => {
    if (!manual && socketUrl) {
      connect();
    }
  }, [socketUrl, manual, connect]);

  useUnmount(() => {
    unmountedRef.current = true;
    disconnect();
  });

  return {
    latestMessage,
    sendMessage: useMemoizedFn(sendMessage),
    connect: useMemoizedFn(connect),
    disconnect: useMemoizedFn(disconnect),
    readyState,
    webSocketIns: websocketRef.current,
  };
}

export default useWebSocket;

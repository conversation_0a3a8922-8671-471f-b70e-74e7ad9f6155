import { useEffect, useMemo, useState, useRef, useCallback } from 'react';
import type { CSSProperties } from 'react';
import useEventListener from '../useEventListener';
import useLatest from '../useLatest';
import useMemoizedFn from '../useMemoizedFn';
import useSize from '../useSize';
import { getTargetElement } from '../utils/domTarget';
import type { BasicTarget } from '../utils/domTarget';
import { isNumber } from '../utils';
import useUpdateEffect from '../useUpdateEffect';

type ItemHeight<T> = (index: number, data: T) => number;

export interface Options<T> {
  containerTarget: BasicTarget;
  wrapperTarget: BasicTarget;
  itemHeight: number | ItemHeight<T>;
  overscan?: number;
}

const useVirtualList = <T = any>(list: T[], options: Options<T>) => {
  const { containerTarget, wrapperTarget, itemHeight, overscan = 5 } = options;

  const itemHeightRef = useLatest(itemHeight);
  const listRef = useLatest(list);

  const size = useSize(containerTarget);

  const scrollTriggerByScrollToFunc = useRef(false);
  const heightCacheRef = useRef<Map<number, number>>(new Map());

  const [targetList, setTargetList] = useState<{ index: number; data: T }[]>([]);
  const [wrapperStyle, setWrapperStyle] = useState<CSSProperties>({});

  const getItemHeight = useCallback((index: number, data: T) => {
    if (isNumber(itemHeightRef.current)) {
      return itemHeightRef.current;
    }

    const cached = heightCacheRef.current.get(index);
    if (cached !== undefined) {
      return cached;
    }

    const height = itemHeightRef.current(index, data);
    heightCacheRef.current.set(index, height);
    return height;
  }, []);

  const getVisibleCount = useCallback((containerHeight: number, fromIndex: number) => {
    if (isNumber(itemHeightRef.current)) {
      return Math.ceil(containerHeight / itemHeightRef.current);
    }

    let sum = 0;
    let endIndex = fromIndex;
    const currentList = listRef.current;

    for (let i = fromIndex; i < currentList.length; i++) {
      const height = getItemHeight(i, currentList[i]);
      sum += height;
      endIndex = i;
      if (sum >= containerHeight) {
        break;
      }
    }
    return endIndex - fromIndex;
  }, [getItemHeight]);

  const getOffset = useCallback((scrollTop: number) => {
    if (isNumber(itemHeightRef.current)) {
      return Math.floor(scrollTop / itemHeightRef.current);
    }

    let sum = 0;
    let offset = 0;
    const currentList = listRef.current;

    for (let i = 0; i < currentList.length; i++) {
      const height = getItemHeight(i, currentList[i]);
      sum += height;
      if (sum >= scrollTop) {
        offset = i;
        break;
      }
    }
    return offset;
  }, [getItemHeight]);

  const getDistanceTop = useCallback((index: number) => {
    if (isNumber(itemHeightRef.current)) {
      return index * itemHeightRef.current;
    }

    let height = 0;
    const currentList = listRef.current;
    for (let i = 0; i < index && i < currentList.length; i++) {
      height += getItemHeight(i, currentList[i]);
    }
    return height;
  }, [getItemHeight]);

  const totalHeight = useMemo(() => {
    if (isNumber(itemHeightRef.current)) {
      return list.length * itemHeightRef.current;
    }

    let height = 0;
    for (let i = 0; i < list.length; i++) {
      height += getItemHeight(i, list[i]);
    }
    return height;
  }, [list, getItemHeight]);

  const calculateRange = useCallback(() => {
    const container = getTargetElement(containerTarget);

    if (!container) {
      return;
    }

    const { scrollTop, clientHeight } = container;
    const currentList = listRef.current;

    const offset = getOffset(scrollTop);
    const visibleCount = getVisibleCount(clientHeight, offset);

    const start = Math.max(0, offset - overscan);
    const end = Math.min(currentList.length, offset + visibleCount + overscan);

    const offsetTop = getDistanceTop(start);

    const newWrapperStyle = {
      height: totalHeight - offsetTop + 'px',
      marginTop: offsetTop + 'px',
    };

    const newTargetList = currentList.slice(start, end).map((ele, index) => ({
      data: ele,
      index: index + start,
    }));

    setWrapperStyle(newWrapperStyle);
    setTargetList(newTargetList);
  }, [containerTarget, getOffset, getVisibleCount, overscan, getDistanceTop, totalHeight]);

  useUpdateEffect(() => {
    const wrapper = getTargetElement(wrapperTarget) as HTMLElement;
    if (wrapper) {
      Object.keys(wrapperStyle).forEach((key) => (wrapper.style[key] = wrapperStyle[key]));
    }
  }, [wrapperStyle]);

  useEffect(() => {
    heightCacheRef.current.clear();
  }, [list]);

  useEffect(() => {
    if (!size?.width || !size?.height) {
      return;
    }
    calculateRange();
  }, [size?.width, size?.height, calculateRange]);

  const handleScroll = useCallback((e: Event) => {
    if (scrollTriggerByScrollToFunc.current) {
      scrollTriggerByScrollToFunc.current = false;
      return;
    }
    e.preventDefault();
    calculateRange();
  }, [calculateRange]);

  useEventListener('scroll', handleScroll, {
    target: containerTarget,
  });

  const scrollTo = useCallback((index: number) => {
    const container = getTargetElement(containerTarget);
    if (container) {
      scrollTriggerByScrollToFunc.current = true;
      container.scrollTop = getDistanceTop(index);
      calculateRange();
    }
  }, [containerTarget, getDistanceTop, calculateRange]);

  return [targetList, useMemoizedFn(scrollTo)] as const;
};

export default useVirtualList;

{"name": "ahooks", "private": true, "packageManager": "pnpm@10.12.4", "engines": {"pnpm": ">=7 <=10"}, "repository": {"type": "git", "url": "git+https://github.com/alibaba/hooks.git"}, "scripts": {"init": "pnpm install && pnpm run build", "start": "pnpm run dev", "dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider dumi dev", "clean-dist": "rimraf 'packages/*/{lib,es,node_modules,dist}'", "clean": "pnpm run clean-dist && rimraf node_modules", "build": "pnpm -r --filter=./packages/* run build", "test": "jest && pnpm --filter=./packages/* test", "test:strict": "cross-env REACT_MODE=strict jest && pnpm --filter=./packages/* test:cov", "coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls", "lint": "biome lint --fix", "pretty": "biome format --fix --no-errors-on-unmatched", "build:doc": "cross-env NODE_OPTIONS=--openssl-legacy-provider dumi build", "build:doc-github": "node scripts/build-with-relative-paths.js", "pub:doc-surge": "surge ./dist --domain ahooks.js.org", "pub:doc-gitee": "cd ./dist && rm -rf .git && touch .spa && touch .nojekyll && git init && git remote <NAME_EMAIL>:ahooks/ahooks.git && git add -A && git commit -m \"publish docs\" && git push origin main -f && echo https://gitee.com/ahooks/ahooks/pages", "pub:doc": "pnpm run build:doc && pnpm run pub:doc-surge && pnpm run build:doc-github", "pub": "pnpm run build && pnpm -r --filter=./packages/* publish", "pub:beta": "pnpm run build && pnpm -r --filter=./packages/* publish --tag beta", "preinstall": "npx only-allow pnpm", "prepare": "husky install", "commit": "git add -A && czg"}, "devDependencies": {"@alifd/next": "^1.27.32", "@ant-design/icons": "^5.6.1", "@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/plugin-transform-runtime": "^7.19.6", "@biomejs/biome": "^2.0.6", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.3.0", "@types/jest": "^29.4.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/mockjs": "^1.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router": "^5.1.19", "@umijs/fabric": "^2.1.0", "@vitest/coverage-istanbul": "^3.2.4", "antd": "^5.26.3", "babel-plugin-import": "^1.12.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "czg": "^1.12.0", "del": "^5.1.0", "dumi": "^1.1.54", "fast-glob": "^3.2.11", "fs-extra": "^10.0.1", "gray-matter": "^4.0.3", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-typescript": "^6.0.0-alpha.1", "husky": "^8.0.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-localstorage-mock": "^2.4.18", "jest-websocket-mock": "^2.1.0", "jsdom": "^26.1.0", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-drag-listview": "^0.1.6", "react-json-view": "^1.21.3", "react-router": "^6.4.2", "react-shadow": "^20.6.0", "rimraf": "^3.0.2", "surge": "^0.21.3", "ts-jest": "^29.1.1", "typescript": "^5.8.3", "vitest": "^3.2.4", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}
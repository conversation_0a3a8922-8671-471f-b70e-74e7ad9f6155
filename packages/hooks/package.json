{"name": "ahooks", "version": "3.9.0", "description": "react hooks library", "keywords": ["ahooks", "umi hooks", "react hooks"], "main": "./lib/index.js", "module": "./es/index.js", "types": "./lib/index.d.ts", "unpkg": "dist/ahooks.js", "sideEffects": false, "authors": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "repository": "https://github.com/alibaba/hooks", "homepage": "https://github.com/alibaba/hooks", "scripts": {"build": "gulp && webpack-cli"}, "files": ["dist", "lib", "es", "metadata.json", "package.json", "README.md"], "dependencies": {"@babel/runtime": "^7.21.0", "dayjs": "^1.9.1", "intersection-observer": "^0.12.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "react-fast-compare": "^3.2.2", "resize-observer-polyfill": "^1.5.1", "screenfull": "^5.0.0", "tslib": "^2.4.1"}, "devDependencies": {"@alifd/next": "^1.27.32", "@ant-design/icons": "^5.6.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "antd": "^5.26.3", "jest-websocket-mock": "^2.1.0", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-drag-listview": "^0.1.6", "react-json-view": "^1.21.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "gitHead": "11f6ad571bd365c95ecb9409ca3050cbbfc9b34a"}
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有测试文件
function findTestFiles(dir, testFiles = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      findTestFiles(filePath, testFiles);
    } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
      testFiles.push(filePath);
    }
  }

  return testFiles;
}

// 转换单个测试文件
function convertTestFile(filePath) {
  console.log(`Converting: ${filePath}`);

  let content = fs.readFileSync(filePath, 'utf8');

  // 添加 vitest 导入（如果还没有的话）
  if (!content.includes('from \'vitest\'')) {
    content = 'import { describe, expect, test, it, beforeEach, afterEach, beforeAll, afterAll, vi } from \'vitest\';\n' + content;
  }

  // 替换所有 jest 相关的调用为 vi
  content = content.replace(/\bjest\./g, 'vi.');
  content = content.replace(/\bjest\b(?!\w)/g, 'vi');

  fs.writeFileSync(filePath, content);
  console.log(`✓ Converted: ${filePath}`);
}

// 主函数
function main() {
  const hooksDir = path.join(__dirname, 'packages/hooks/src');

  if (!fs.existsSync(hooksDir)) {
    console.error('hooks directory not found!');
    process.exit(1);
  }

  const testFiles = findTestFiles(hooksDir);
  console.log(`Found ${testFiles.length} test files`);

  for (const testFile of testFiles) {
    try {
      convertTestFile(testFile);
    } catch (error) {
      console.error(`Error converting ${testFile}:`, error.message);
    }
  }

  console.log('\n✅ All test files converted to Vitest!');
}

main();

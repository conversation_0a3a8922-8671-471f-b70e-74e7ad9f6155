import { describe, expect, test, it, beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import useUnmount from '../index';
describe('useUnmount', () => {
  test('useUnmount should work', async () => {
    const fn = vi.fn();
    const hook = renderHook(() => useUnmount(fn));
    expect(fn).toBeCalledTimes(0);
    hook.rerender();
    expect(fn).toBeCalledTimes(0);
    hook.unmount();
    expect(fn).toBeCalledTimes(1);
  });

  // test('should output error when fn is not a function', () => {
  //   const errSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  //   renderHook(() => useUnmount(1 as any));
  //   expect(errSpy).toBeCalledWith('useUnmount expected parameter is a function, got number');
  //   errSpy.mockRestore();
  // });
});

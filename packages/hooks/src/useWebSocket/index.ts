import { useEffect, useRef, useState, useCallback } from 'react';
import useLatest from '../useLatest';
import useMemoizedFn from '../useMemoizedFn';
import useUnmount from '../useUnmount';

export enum ReadyState {
  Connecting = 0,
  Open = 1,
  Closing = 2,
  Closed = 3,
}

export interface Options {
  reconnectLimit?: number;
  reconnectInterval?: number;
  manual?: boolean;
  heartbeatInterval?: number;
  heartbeatMessage?: string | (() => string);
  onOpen?: (event: WebSocketEventMap['open'], instance: WebSocket) => void;
  onClose?: (event: WebSocketEventMap['close'], instance: WebSocket) => void;
  onMessage?: (message: WebSocketEventMap['message'], instance: WebSocket) => void;
  onError?: (event: WebSocketEventMap['error'], instance: WebSocket) => void;

  protocols?: string | string[];
}

export interface Result {
  latestMessage?: WebSocketEventMap['message'];
  sendMessage: WebSocket['send'];
  disconnect: () => void;
  connect: () => void;
  readyState: ReadyState;
  webSocketIns?: WebSocket;
}

function useWebSocket(socketUrl: string, options: Options = {}): Result {
  const {
    reconnectLimit = 3,
    reconnectInterval = 3 * 1000,
    heartbeatInterval = 30 * 1000,
    heartbeatMessage = 'ping',
    manual = false,
    onOpen,
    onClose,
    onMessage,
    onError,
    protocols,
  } = options;

  const onOpenRef = useLatest(onOpen);
  const onCloseRef = useLatest(onClose);
  const onMessageRef = useLatest(onMessage);
  const onErrorRef = useLatest(onError);

  const reconnectTimesRef = useRef(0);
  const reconnectTimerRef = useRef<ReturnType<typeof setTimeout>>();
  const heartbeatTimerRef = useRef<ReturnType<typeof setTimeout>>();
  const websocketRef = useRef<WebSocket>();
  const unmountedRef = useRef(false);
  const messageQueueRef = useRef<(string | ArrayBufferLike | Blob | ArrayBufferView)[]>([]);
  const connectingRef = useRef(false);

  const [latestMessage, setLatestMessage] = useState<MessageEvent>();
  const [readyState, setReadyState] = useState<ReadyState>(ReadyState.Closed);

  const clearTimers = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = undefined;
    }
    if (heartbeatTimerRef.current) {
      clearTimeout(heartbeatTimerRef.current);
      heartbeatTimerRef.current = undefined;
    }
  }, []);

  const startHeartbeat = useCallback(() => {
    if (heartbeatTimerRef.current) {
      clearTimeout(heartbeatTimerRef.current);
    }

    heartbeatTimerRef.current = setTimeout(() => {
      if (websocketRef.current?.readyState === ReadyState.Open) {
        try {
          const message = typeof heartbeatMessage === 'function' ? heartbeatMessage() : heartbeatMessage;
          websocketRef.current.send(message);
          startHeartbeat();
        } catch (error) {
          console.error('Heartbeat failed:', error);
        }
      }
    }, heartbeatInterval);
  }, [heartbeatInterval, heartbeatMessage]);

  const processMessageQueue = useCallback(() => {
    if (websocketRef.current?.readyState === ReadyState.Open && messageQueueRef.current.length > 0) {
      const queue = [...messageQueueRef.current];
      messageQueueRef.current = [];

      queue.forEach(message => {
        try {
          websocketRef.current?.send(message);
        } catch (error) {
          console.error('Failed to send queued message:', error);
          messageQueueRef.current.push(message);
        }
      });
    }
  }, []);

  const reconnect = useCallback(() => {
    if (unmountedRef.current || connectingRef.current) {
      return;
    }

    if (reconnectTimesRef.current < reconnectLimit) {
      clearTimers();

      reconnectTimerRef.current = setTimeout(() => {
        if (!unmountedRef.current && !connectingRef.current) {
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          connectWs();
          reconnectTimesRef.current++;
        }
      }, reconnectInterval);
    }
  }, [reconnectLimit, reconnectInterval, clearTimers]);

  const connectWs = useCallback(() => {
    if (unmountedRef.current || connectingRef.current) {
      return;
    }

    connectingRef.current = true;
    clearTimers();

    if (websocketRef.current) {
      websocketRef.current.close();
    }

    try {
      const ws = new WebSocket(socketUrl, protocols);
      setReadyState(ReadyState.Connecting);

      ws.onerror = (event) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        connectingRef.current = false;
        onErrorRef.current?.(event, ws);
        setReadyState(ws.readyState || ReadyState.Closed);
        reconnect();
      };

      ws.onopen = (event) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        connectingRef.current = false;
        onOpenRef.current?.(event, ws);
        reconnectTimesRef.current = 0;
        setReadyState(ws.readyState || ReadyState.Open);
        startHeartbeat();
        processMessageQueue();
      };

      ws.onmessage = (message: WebSocketEventMap['message']) => {
        if (websocketRef.current !== ws || unmountedRef.current) {
          return;
        }
        onMessageRef.current?.(message, ws);
        setLatestMessage(message);
      };

      ws.onclose = (event) => {
        if (unmountedRef.current) {
          return;
        }
        connectingRef.current = false;
        clearTimers();
        onCloseRef.current?.(event, ws);

        if (websocketRef.current === ws) {
          setReadyState(ws.readyState || ReadyState.Closed);
          reconnect();
        }
      };

      websocketRef.current = ws;
    } catch (error) {
      connectingRef.current = false;
      setReadyState(ReadyState.Closed);
      onErrorRef.current?.(error as Event, websocketRef.current!);
    }
  }, [socketUrl, protocols, clearTimers, reconnect, startHeartbeat, processMessageQueue]);

  const sendMessage = useCallback((message: string | ArrayBufferLike | Blob | ArrayBufferView) => {
    if (readyState === ReadyState.Open && websocketRef.current) {
      try {
        websocketRef.current.send(message);
      } catch (error) {
        console.error('Failed to send message:', error);
        messageQueueRef.current.push(message);
        throw error;
      }
    } else {
      messageQueueRef.current.push(message);
      if (readyState === ReadyState.Closed) {
        connect();
      }
    }
  }, [readyState]);

  const connect = useCallback(() => {
    reconnectTimesRef.current = 0;
    connectingRef.current = false;
    connectWs();
  }, [connectWs]);

  const disconnect = useCallback(() => {
    clearTimers();
    reconnectTimesRef.current = reconnectLimit;
    connectingRef.current = false;
    messageQueueRef.current = [];

    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = undefined;
    }

    setReadyState(ReadyState.Closed);
  }, [clearTimers, reconnectLimit]);

  useEffect(() => {
    if (!manual && socketUrl) {
      connect();
    }
  }, [socketUrl, manual, connect]);

  useUnmount(() => {
    unmountedRef.current = true;
    disconnect();
  });

  return {
    latestMessage,
    sendMessage: useMemoizedFn(sendMessage),
    connect: useMemoizedFn(connect),
    disconnect: useMemoizedFn(disconnect),
    readyState,
    webSocketIns: websocketRef.current,
  };
}

export default useWebSocket;

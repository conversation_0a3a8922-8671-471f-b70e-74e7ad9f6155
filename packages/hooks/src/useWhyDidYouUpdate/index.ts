import { useEffect, useRef, useMemo } from 'react';

export type IProps = Record<string, any>;

interface ChangedProp {
  from: any;
  to: any;
}

function useWhyDidYouUpdate(componentName: string, props: IProps) {
  const prevPropsRef = useRef<IProps>();

  const changedProps = useMemo(() => {
    if (!prevPropsRef.current) {
      return null;
    }

    const changes: Record<string, ChangedProp> = {};
    const prevProps = prevPropsRef.current;

    const allKeys = new Set([...Object.keys(prevProps), ...Object.keys(props)]);

    for (const key of allKeys) {
      const prevValue = prevProps[key];
      const currentValue = props[key];

      if (!Object.is(prevValue, currentValue)) {
        changes[key] = {
          from: prevValue,
          to: currentValue,
        };
      }
    }

    return Object.keys(changes).length > 0 ? changes : null;
  }, [props]);

  useEffect(() => {
    if (changedProps) {
      console.group(`[why-did-you-update] ${componentName}`);
      Object.entries(changedProps).forEach(([key, { from, to }]) => {
        console.log(`%c${key}:`, 'color: #ff6b6b; font-weight: bold;', { from, to });
      });
      console.groupEnd();
    }

    prevPropsRef.current = props;
  });
}

export default useWhyDidYouUpdate;

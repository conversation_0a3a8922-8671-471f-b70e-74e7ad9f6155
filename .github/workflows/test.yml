name: Test CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        mode: ['normal', 'strict']
        node-version: [20, 22]

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Get pnpm store directory
        id: pnpm-cache
        run: |
          echo "pnpm_cache_dir=$(pnpm store path)" >> "$GITHUB_OUTPUT"

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.pnpm_cache_dir }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: pnpm run install, build
        run: |
          pnpm run init

      - name: test with react normal mode
        if: ${{ matrix.mode == 'normal' }}
        run: |
          pnpm run test

      - name: test with react strict mode
        if: ${{ matrix.mode == 'strict' }}
        run: |
          pnpm run test:strict

import { describe, expect, test, it, beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import useIsomorphicLayoutEffect from '../index';

describe('useIsomorphicLayoutEffect', () => {
  const callback = vi.fn();
  const { result } = renderHook(() => useIsomorphicLayoutEffect(callback));

  it('cheak return value', () => {
    expect(result.current).toBeUndefined();
  });
});

import { useMemo, useRef, useState, useCallback } from 'react';
import useEventListener from '../useEventListener';
import useMemoizedFn from '../useMemoizedFn';
import useRequest from '../useRequest';
import useUpdateEffect from '../useUpdateEffect';
import { getTargetElement } from '../utils/domTarget';
import { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';
import type { Data, InfiniteScrollOptions, Service } from './types';

const useInfiniteScroll = <TData extends Data>(
  service: Service<TData>,
  options: InfiniteScrollOptions<TData> = {},
) => {
  const {
    target,
    isNoMore,
    threshold = 100,
    direction = 'bottom',
    reloadDeps = [],
    manual,
    onBefore,
    onSuccess,
    onError,
    onFinally,
  } = options;

  const [finalData, setFinalData] = useState<TData>();
  const [loadingMore, setLoadingMore] = useState(false);
  const isScrollToTop = direction === 'top';
  const lastScrollTop = useRef<number>(0);
  const scrollBottom = useRef<number>(0);
  const isLoadingRef = useRef(false);

  const noMore = useMemo(() => {
    if (!isNoMore || !finalData) {
      return false;
    }
    return isNoMore(finalData);
  }, [isNoMore, finalData]);

  const updateFinalData = useCallback((currentData: TData, lastData?: TData) => {
    if (!lastData) {
      setFinalData({
        ...currentData,
        list: [...(currentData.list ?? [])],
      });
    } else {
      setFinalData({
        ...currentData,
        list: isScrollToTop
          ? [...(currentData.list ?? []), ...(lastData.list ?? [])]
          : [...(lastData.list ?? []), ...(currentData.list ?? [])],
      });
    }
  }, [isScrollToTop]);

  const { loading, error, run, runAsync, cancel } = useRequest(
    async (lastData?: TData) => {
      const currentData = await service(lastData);
      updateFinalData(currentData, lastData);
      return currentData;
    },
    {
      manual,
      onFinally: (_, d, e) => {
        setLoadingMore(false);
        isLoadingRef.current = false;
        onFinally?.(d, e);
      },
      onBefore: () => {
        isLoadingRef.current = true;
        onBefore?.();
      },
      onSuccess: (d) => {
        if (isScrollToTop) {
          setTimeout(() => {
            const el = getTargetElement(target);
            const targetEl = el === document ? document.documentElement : el;
            if (targetEl) {
              const scrollHeight = getScrollHeight(targetEl);
              (targetEl as Element).scrollTo(0, scrollHeight - scrollBottom.current);
            }
          }, 0);
        }
        onSuccess?.(d);
      },
      onError: (e) => onError?.(e),
    },
  );

  const loadMore = useCallback(() => {
    if (noMore || isLoadingRef.current) {
      return;
    }
    setLoadingMore(true);
    run(finalData);
  }, [noMore, finalData, run]);

  const loadMoreAsync = useCallback(() => {
    if (noMore || isLoadingRef.current) {
      return Promise.reject(new Error('Cannot load more'));
    }
    setLoadingMore(true);
    return runAsync(finalData);
  }, [noMore, finalData, runAsync]);

  const reload = useCallback(() => {
    setLoadingMore(false);
    isLoadingRef.current = false;
    return run();
  }, [run]);

  const reloadAsync = useCallback(() => {
    setLoadingMore(false);
    isLoadingRef.current = false;
    return runAsync();
  }, [runAsync]);

  const checkScrollPosition = useCallback((targetEl: Element | Document) => {
    const element = targetEl === document ? document.documentElement : (targetEl as Element);
    const scrollTop = getScrollTop(element);
    const scrollHeight = getScrollHeight(element);
    const clientHeight = getClientHeight(element);

    if (isScrollToTop) {
      const shouldLoad = lastScrollTop.current > scrollTop && scrollTop <= threshold;
      lastScrollTop.current = scrollTop;
      scrollBottom.current = scrollHeight - scrollTop;
      return shouldLoad;
    } else {
      return scrollHeight - scrollTop <= clientHeight + threshold;
    }
  }, [isScrollToTop, threshold]);

  const scrollMethod = useCallback(() => {
    if (isLoadingRef.current || noMore) {
      return;
    }

    const el = getTargetElement(target);
    if (!el) {
      return;
    }

    if (checkScrollPosition(el)) {
      loadMore();
    }
  }, [target, checkScrollPosition, loadMore, noMore]);

  useEventListener('scroll', scrollMethod, { target });

  useUpdateEffect(() => {
    run();
  }, [...reloadDeps]);

  return {
    data: finalData,
    loading: !loadingMore && loading,
    error,
    loadingMore,
    noMore,

    loadMore: useMemoizedFn(loadMore),
    loadMoreAsync: useMemoizedFn(loadMoreAsync),
    reload: useMemoizedFn(reload),
    reloadAsync: useMemoizedFn(reloadAsync),
    mutate: setFinalData,
    cancel,
  };
};

export default useInfiniteScroll;

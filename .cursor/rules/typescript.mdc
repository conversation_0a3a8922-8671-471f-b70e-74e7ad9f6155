# TypeScript 规范

## 基本原则

- 所有组件和函数必须提供准确的类型定义
- 尽量避免使用 `any` 类型，尽可能精确地定义类型
- 使用接口而非类型别名定义对象结构
- 导出所有公共接口类型，方便用户使用
- 严格遵循 TypeScript 类型设计原则，确保类型安全
- 确保编译无任何类型错误或警告

## hook 类型定义

- 复杂的数据结构应拆分为多个接口定义
- 所有函数类型应明确定义参数和返回值

## 泛型使用

- 适当使用泛型增强类型灵活性
- 为泛型参数提供合理的默认类型和约束
- 避免过度使用泛型导致类型复杂化
- 在泛型参数上应用限制条件（constraints）确保类型安全
- 为复杂泛型提供类型别名以提高可读性

## 类型合并与扩展

- 使用交叉类型（&）合并多个类型
- 使用 Partial<T>、Pick<T, K>、Omit<T, K> 等工具类型修改现有类型
- 扩展原生 DOM 元素属性时，继承相应的内置类型
- 使用 type 定义联合类型和交叉类型
- 优先使用自带的工具类型，避免重复定义

## 枚举和常量

- 使用字面量联合类型定义有限的选项集合
- 为复杂的枚举值提供类型守卫函数
- 避免使用 `enum`，优先使用联合类型和 `as const`
- 对于关键常量，使用 `as const` 断言确保类型严格
- 为联合类型中的每个值提供适当的注释

## 类型推断与断言

- 尽可能依赖 TypeScript 的类型推断
- 只在必要时使用类型断言（as）
- 使用类型守卫函数进行运行时类型检查
- 尽量避免使用非空断言操作符（!）
- 使用 `instanceof` 和 `typeof` 进行类型守卫
- 为自定义类型创建类型谓词（type predicates）函数

## JSDoc 注释

- 为复杂的类型、函数、hook 添加 JSDoc 注释
- 使用 `@deprecated` 标记已废弃的 API
- 在注释中提供使用示例
- 说明参数和返回值的含义与约束
- 在 interface 和重要类型定义上添加文档注释

## 类型兼容性

- 确保类型定义兼容不同版本的 React
- 避免使用实验性或不稳定的 TypeScript 特性
- 为第三方库未提供的类型编写声明文件
- 使用条件类型处理复杂的类型逻辑
- 验证类型在不同 TypeScript 版本下的兼容性

## 严格使用 TypeScript 类型

- 导出组件类型和接口
- 避免使用 any，优先使用 unknown
- 组件 Props 使用 interface 定义
- 使用明确的命名约定
- 合理使用泛型提高复用性
- 导出类型时使用 export type
- 组件属性使用 JSDoc 注释说明用途

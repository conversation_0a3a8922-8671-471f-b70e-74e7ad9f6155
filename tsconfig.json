{"compilerOptions": {"target": "ES5", "moduleResolution": "node", "jsx": "react", "esModuleInterop": true, "downlevelIteration": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@ahooksjs/use-url-state": ["./packages/use-url-state/src/index.ts"]}, "allowJs": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "declaration": false, "strictNullChecks": true, "importHelpers": true}, "exclude": ["node_modules", "lib", "es", "dist", "example", "vitest.config.ts"]}
{"name": "@ahooksjs/use-url-state", "version": "3.6.0", "description": "A hook that stores the state into url query parameters.", "main": "./lib/index.js", "module": "./es/index.js", "types": "./lib/index.d.ts", "unpkg": "dist/ahooks-use-url-state.js", "files": ["dist", "lib", "es", "package.json"], "repository": {"type": "git", "url": "git+https://github.com/alibaba/hooks.git"}, "scripts": {"build": "gulp && webpack-cli", "test": "vitest run", "test:cov": "vitest run --coverage"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "license": "MIT", "bugs": {"url": "https://github.com/alibaba/hooks/issues"}, "homepage": "https://github.com/alibaba/hooks", "gitHead": "11f6ad571bd365c95ecb9409ca3050cbbfc9b34a", "dependencies": {"@babel/runtime": "^7.21.0", "ahooks": "^3.4.1", "query-string": "^8.1.0", "tslib": "^2.4.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-router": "^5.0.0 || ^6.0.0 || ^7.0.0"}}
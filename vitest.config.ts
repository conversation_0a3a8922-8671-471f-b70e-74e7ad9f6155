import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.ts'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/es/**',
      '**/lib/**',
      '**/.history/**',
      '**/packages/use-url-state/**',
      '**/packages/hooks/es/**',
      '**/packages/hooks/lib/**',
      '**/packages/hooks/dist/**',
      '**/packages/hooks/src/**/tests/**',
    ],
    coverage: {
      provider: 'istanbul',
      include: [
        '**/src/**/*.{js,jsx,ts,tsx}',
      ],
      exclude: [
        '**/demo/**',
        '**/example/**',
        '**/es/**',
        '**/lib/**',
        '**/dist/**',
        '**/packages/hooks/src/**/tests/**',
        '**/node_modules/**',
      ],
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './packages/hooks/src'),
    },
  },
});

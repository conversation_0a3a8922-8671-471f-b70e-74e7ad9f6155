import { useEffect, useMemo, useState, useRef } from 'react';
import type { CSSProperties } from 'react';
import useEventListener from '../useEventListener';
import useLatest from '../useLatest';
import useMemoizedFn from '../useMemoizedFn';
import useSize from '../useSize';
import { getTargetElement } from '../utils/domTarget';
import type { BasicTarget } from '../utils/domTarget';
import { isNumber } from '../utils';
import useUpdateEffect from '../useUpdateEffect';

type ItemHeight<T> = (index: number, data: T) => number;

export interface Options<T> {
  containerTarget: BasicTarget;
  wrapperTarget: BasicTarget;
  itemHeight: number | ItemHeight<T>;
  overscan?: number;
}

const useVirtualList = <T = any>(list: T[], options: Options<T>) => {
  const { containerTarget, wrapperTarget, itemHeight, overscan = 5 } = options;

  const itemHeightRef = useLatest(itemHeight);
  const size = useSize(containerTarget);

  const scrollTriggerByScrollToFunc = useRef(false);
  const heightCacheRef = useRef<number[]>([]);
  const prefixSumRef = useRef<number[]>([]);
  const lastScrollTop = useRef(0);
  const rafId = useRef<number>();

  const [targetList, setTargetList] = useState<{ index: number; data: T }[]>([]);
  const [wrapperStyle, setWrapperStyle] = useState<CSSProperties>({});

  const getItemHeight = (index: number, data: T): number => {
    if (isNumber(itemHeightRef.current)) {
      return itemHeightRef.current;
    }

    if (heightCacheRef.current[index] !== undefined) {
      return heightCacheRef.current[index];
    }

    const height = itemHeightRef.current(index, data);
    heightCacheRef.current[index] = height;
    return height;
  };

  const buildPrefixSum = () => {
    if (isNumber(itemHeightRef.current)) {
      prefixSumRef.current = [];
      return;
    }

    const prefixSum = [0];
    for (let i = 0; i < list.length; i++) {
      const height = getItemHeight(i, list[i]);
      prefixSum[i + 1] = prefixSum[i] + height;
    }
    prefixSumRef.current = prefixSum;
  };

  const getOffset = (scrollTop: number): number => {
    if (isNumber(itemHeightRef.current)) {
      return Math.floor(scrollTop / itemHeightRef.current);
    }

    const prefixSum = prefixSumRef.current;
    if (prefixSum.length === 0) return 0;

    let left = 0;
    let right = prefixSum.length - 1;

    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (prefixSum[mid] < scrollTop) {
        left = mid + 1;
      } else {
        right = mid;
      }
    }

    return Math.max(0, left - 1);
  };

  const getVisibleCount = (containerHeight: number, fromIndex: number): number => {
    if (isNumber(itemHeightRef.current)) {
      return Math.ceil(containerHeight / itemHeightRef.current);
    }

    let sum = 0;
    let count = 0;

    for (let i = fromIndex; i < list.length; i++) {
      const height = getItemHeight(i, list[i]);
      sum += height;
      count++;
      if (sum >= containerHeight) {
        break;
      }
    }
    return count;
  };

  const getDistanceTop = (index: number): number => {
    if (isNumber(itemHeightRef.current)) {
      return index * itemHeightRef.current;
    }

    const prefixSum = prefixSumRef.current;
    return prefixSum[index] || 0;
  };

  const totalHeight = useMemo(() => {
    if (isNumber(itemHeightRef.current)) {
      return list.length * itemHeightRef.current;
    }

    const prefixSum = prefixSumRef.current;
    return prefixSum[prefixSum.length - 1] || 0;
  }, [list]);

  const calculateRange = () => {
    const container = getTargetElement(containerTarget);
    if (!container) return;

    const { scrollTop, clientHeight } = container;
    const offset = getOffset(scrollTop);
    const visibleCount = getVisibleCount(clientHeight, offset);

    const start = Math.max(0, offset - overscan);
    const end = Math.min(list.length, offset + visibleCount + overscan);

    const offsetTop = getDistanceTop(start);

    setWrapperStyle({
      height: totalHeight - offsetTop + 'px',
      marginTop: offsetTop + 'px',
    });

    setTargetList(
      list.slice(start, end).map((ele, index) => ({
        data: ele,
        index: index + start,
      }))
    );
  };

  const throttledCalculateRange = () => {
    if (rafId.current) {
      cancelAnimationFrame(rafId.current);
    }

    rafId.current = requestAnimationFrame(() => {
      calculateRange();
      rafId.current = undefined;
    });
  };

  useUpdateEffect(() => {
    const wrapper = getTargetElement(wrapperTarget) as HTMLElement;
    if (wrapper) {
      Object.keys(wrapperStyle).forEach((key) => (wrapper.style[key] = wrapperStyle[key]));
    }
  }, [wrapperStyle]);

  useEffect(() => {
    heightCacheRef.current = [];
    buildPrefixSum();
  }, [list]);

  useEffect(() => {
    if (!size?.width || !size?.height) {
      return;
    }
    buildPrefixSum();
    calculateRange();
  }, [size?.width, size?.height, totalHeight]);

  useEffect(() => {
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  const handleScroll = (e: Event) => {
    if (scrollTriggerByScrollToFunc.current) {
      scrollTriggerByScrollToFunc.current = false;
      return;
    }

    const container = e.target as Element;
    const currentScrollTop = container.scrollTop;

    if (Math.abs(currentScrollTop - lastScrollTop.current) < 1) {
      return;
    }

    lastScrollTop.current = currentScrollTop;
    e.preventDefault();
    throttledCalculateRange();
  };

  useEventListener('scroll', handleScroll, {
    target: containerTarget,
  });

  const scrollTo = (index: number) => {
    const container = getTargetElement(containerTarget);
    if (container) {
      scrollTriggerByScrollToFunc.current = true;
      container.scrollTop = getDistanceTop(index);
      calculateRange();
    }
  };

  return [targetList, useMemoizedFn(scrollTo)] as const;
};

export default useVirtualList;

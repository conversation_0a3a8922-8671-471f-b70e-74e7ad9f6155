{"name": "ahooks", "private": true, "packageManager": "pnpm@9.6.0", "engines": {"pnpm": ">=7 <10"}, "repository": {"type": "git", "url": "git+https://github.com/alibaba/hooks.git"}, "scripts": {"init": "pnpm install && pnpm run build", "start": "pnpm run dev", "dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider dumi dev", "clean-dist": "rimraf 'packages/*/{lib,es,node_modules,dist}'", "clean": "pnpm run clean-dist && rimraf node_modules", "build": "pnpm -r --filter=./packages/* run build", "test": "jest", "coveralls": "jest --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint --ignore-pattern **/__tests__/* --ignore-pattern **/demo/* \"packages/*/src/**/*.{ts,tsx}\" --cache", "pretty": "biome format --fix --staged", "build:doc": "cross-env NODE_OPTIONS=--openssl-legacy-provider dumi build", "build:doc-github": "node scripts/build-with-relative-paths.js", "pub:doc-surge": "surge ./dist --domain ahooks.js.org", "pub:doc-gitee": "cd ./dist && rm -rf .git && touch .spa && touch .nojekyll && git init && git remote <NAME_EMAIL>:ahooks/ahooks.git && git add -A && git commit -m \"publish docs\" && git push origin main -f && echo https://gitee.com/ahooks/ahooks/pages", "pub:doc": "pnpm run build:doc && pnpm run pub:doc-surge && pnpm run build:doc-github", "pub": "pnpm run build && pnpm -r --filter=./packages/* publish", "pub:beta": "pnpm run build && pnpm -r --filter=./packages/* publish --tag beta", "preinstall": "npx only-allow pnpm", "prepare": "husky install", "test:strict": "cross-env REACT_MODE=strict jest"}, "devDependencies": {"@ant-design/icons": "^5.6.1", "@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/plugin-transform-runtime": "^7.19.6", "@biomejs/biome": "^1.9.4", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.3.0", "@types/jest": "^29.4.0", "@types/mockjs": "^1.0.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@types/react-router": "^5.1.19", "@umijs/fabric": "^2.1.0", "antd": "^5.26.3", "babel-plugin-import": "^1.12.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "del": "^5.1.0", "dumi": "^1.1.54", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "fast-glob": "^3.2.11", "fs-extra": "^10.0.1", "gray-matter": "^4.0.3", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-typescript": "^6.0.0-alpha.1", "husky": "^8.0.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-localstorage-mock": "^2.4.18", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-drag-listview": "^0.1.6", "react-router": "^6.4.2", "react-shadow": "^20.6.0", "rimraf": "^3.0.2", "surge": "^0.21.3", "ts-jest": "^29.1.1", "typescript": "^5.8.3", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-merge": "^4.2.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}
{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "files": {"ignoreUnknown": true}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "linter": {"rules": {"style": {"noNonNullAssertion": "off"}, "correctness": {"useHookAtTopLevel": "error"}, "suspicious": {"noExplicitAny": "off"}}}, "formatter": {"lineWidth": 100, "indentStyle": "space"}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}, "formatter": {"quoteStyle": "single"}}, "css": {"parser": {"cssModules": true}, "formatter": {"enabled": true}, "linter": {"enabled": true}}}
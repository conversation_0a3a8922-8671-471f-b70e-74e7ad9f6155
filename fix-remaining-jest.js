#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有测试文件
function findTestFiles(dir, testFiles = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findTestFiles(filePath, testFiles);
    } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
      testFiles.push(filePath);
    }
  }
  
  return testFiles;
}

// 修复单个测试文件中剩余的jest引用
function fixTestFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // 检查是否还有jest引用
  if (content.includes('jest.')) {
    console.log(`Fixing jest references in: ${filePath}`);
    content = content.replace(/jest\./g, 'vi.');
    changed = true;
  }
  
  // 检查是否缺少vitest导入
  if (!content.includes('from \'vitest\'') && (content.includes('describe(') || content.includes('it(') || content.includes('expect('))) {
    console.log(`Adding vitest import to: ${filePath}`);
    content = 'import { describe, expect, test, it, beforeEach, afterEach, beforeAll, afterAll, vi } from \'vitest\';\n' + content;
    changed = true;
  }
  
  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`✓ Fixed: ${filePath}`);
  }
}

// 主函数
function main() {
  const hooksDir = path.join(__dirname, 'packages/hooks/src');
  
  if (!fs.existsSync(hooksDir)) {
    console.error('hooks directory not found!');
    process.exit(1);
  }
  
  const testFiles = findTestFiles(hooksDir);
  console.log(`Checking ${testFiles.length} test files for remaining jest references...`);
  
  let fixedCount = 0;
  for (const testFile of testFiles) {
    try {
      const beforeContent = fs.readFileSync(testFile, 'utf8');
      fixTestFile(testFile);
      const afterContent = fs.readFileSync(testFile, 'utf8');
      if (beforeContent !== afterContent) {
        fixedCount++;
      }
    } catch (error) {
      console.error(`Error fixing ${testFile}:`, error.message);
    }
  }
  
  console.log(`\n✅ Fixed ${fixedCount} files with remaining jest references!`);
}

main();

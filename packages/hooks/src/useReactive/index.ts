import { useRef, useCallback } from 'react';
import isPlainObject from 'lodash/isPlainObject';
import useCreation from '../useCreation';
import useUpdate from '../useUpdate';

const proxyMap = new WeakMap();
const rawMap = new WeakMap();
const MAX_DEPTH = 10;

const isProxyable = (val: any): boolean => {
  return val !== null && (isPlainObject(val) || Array.isArray(val));
};

let batchUpdateTimer: number | null = null;
const batchCallbacks = new Set<() => void>();

const batchUpdate = (callback: () => void) => {
  batchCallbacks.add(callback);

  if (batchUpdateTimer === null) {
    batchUpdateTimer = window.setTimeout(() => {
      const callbacks = Array.from(batchCallbacks);
      batchCallbacks.clear();
      batchUpdateTimer = null;

      callbacks.forEach(cb => cb());
    }, 0);
  }
};

function observer<T extends Record<string, any>>(
  initialVal: T,
  cb: () => void,
  depth: number = 0
): T {
  if (depth > MAX_DEPTH) {
    console.warn('useReactive: Maximum proxy depth exceeded, returning original value');
    return initialVal;
  }

  const existingProxy = proxyMap.get(initialVal);
  if (existingProxy) {
    return existingProxy;
  }

  if (rawMap.has(initialVal)) {
    return initialVal;
  }

  const proxy = new Proxy<T>(initialVal, {
    get(target, key, receiver) {
      const res = Reflect.get(target, key, receiver);

      const descriptor = Reflect.getOwnPropertyDescriptor(target, key);
      if (!descriptor?.configurable && !descriptor?.writable) {
        return res;
      }

      return isProxyable(res) ? observer(res, cb, depth + 1) : res;
    },
    set(target, key, val) {
      const oldVal = target[key as keyof T];
      const ret = Reflect.set(target, key, val);

      if (oldVal !== val) {
        if (Array.isArray(target)) {
          batchUpdate(cb);
        } else {
          cb();
        }
      }
      return ret;
    },
    deleteProperty(target, key) {
      const hadKey = key in target;
      const ret = Reflect.deleteProperty(target, key);

      if (hadKey && ret) {
        if (Array.isArray(target)) {
          batchUpdate(cb);
        } else {
          cb();
        }
      }
      return ret;
    },
  });

  proxyMap.set(initialVal, proxy);
  rawMap.set(proxy, initialVal);

  return proxy;
}

function useReactive<S extends Record<string, any>>(initialState: S): S {
  const update = useUpdate();
  const stateRef = useRef<S>(initialState);

  const updateCallback = useCallback(() => {
    update();
  }, [update]);

  const state = useCreation(() => {
    return observer(stateRef.current, updateCallback, 0);
  }, []);

  return state;
}

export default useReactive;
